{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Images/dmo_header.7e25eyq1m0.png", "AssetFile": "Images/dmo_header.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "47428"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oe6PvLVqQ1rXVH26PiWOd7RR8NCrNbWy84doET457LU=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7e25eyq1m0"}, {"Name": "integrity", "Value": "sha256-oe6PvLVqQ1rXVH26PiWOd7RR8NCrNbWy84doET457LU="}, {"Name": "label", "Value": "Images/dmo_header.png"}]}, {"Route": "Images/dmo_header.png", "AssetFile": "Images/dmo_header.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "47428"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oe6PvLVqQ1rXVH26PiWOd7RR8NCrNbWy84doET457LU=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oe6PvLVqQ1rXVH26PiWOd7RR8NCrNbWy84doET457LU="}]}, {"Route": "Images/dmo_login_v1.iiq4zs4q7p.jpg", "AssetFile": "Images/dmo_login_v1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "67363"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xu23CYab4g7zCKhYeF6RZZMDaPey10kxFSaHUguseF8=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iiq4zs4q7p"}, {"Name": "integrity", "Value": "sha256-xu23CYab4g7zCKhYeF6RZZMDaPey10kxFSaHUguseF8="}, {"Name": "label", "Value": "Images/dmo_login_v1.jpg"}]}, {"Route": "Images/dmo_login_v1.jpg", "AssetFile": "Images/dmo_login_v1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "67363"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xu23CYab4g7zCKhYeF6RZZMDaPey10kxFSaHUguseF8=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xu23CYab4g7zCKhYeF6RZZMDaPey10kxFSaHUguseF8="}]}, {"Route": "Images/dmo_login_v2.7jfkridg86.png", "AssetFile": "Images/dmo_login_v2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "394161"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"o94ZfQjT2Xds8gPWAmkugrg7ZaOIDyH3fgC0DDfsDoI=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7jfkridg86"}, {"Name": "integrity", "Value": "sha256-o94ZfQjT2Xds8gPWAmkugrg7ZaOIDyH3fgC0DDfsDoI="}, {"Name": "label", "Value": "Images/dmo_login_v2.png"}]}, {"Route": "Images/dmo_login_v2.png", "AssetFile": "Images/dmo_login_v2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "394161"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"o94ZfQjT2Xds8gPWAmkugrg7ZaOIDyH3fgC0DDfsDoI=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o94ZfQjT2Xds8gPWAmkugrg7ZaOIDyH3fgC0DDfsDoI="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "505977"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SEX58VQYAJIaU7fd9SmPPYHxZChA4z4wsKgCU8jDtCs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Nov 2022 19:06:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SEX58VQYAJIaU7fd9SmPPYHxZChA4z4wsKgCU8jDtCs="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41479"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rSLqMQHO+xaq+NCakNWOhiM5bnyLNb4zrzbFS4ljy58=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Nov 2022 19:06:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rSLqMQHO+xaq+NCakNWOhiM5bnyLNb4zrzbFS4ljy58="}]}, {"Route": "_content1/MudBlazor/MudBlazor.min.4zzcyv4eu3.css", "AssetFile": "_content1/MudBlazor/MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "505971"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ID+YQTHdxOAQwL3HkDNmN+gvKpDMbSCI90Hlsl4cfE0=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4zzcyv4eu3"}, {"Name": "integrity", "Value": "sha256-ID+YQTHdxOAQwL3HkDNmN+gvKpDMbSCI90Hlsl4cfE0="}, {"Name": "label", "Value": "_content1/MudBlazor/MudBlazor.min.css"}]}, {"Route": "_content1/MudBlazor/MudBlazor.min.css", "AssetFile": "_content1/MudBlazor/MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "505971"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ID+YQTHdxOAQwL3HkDNmN+gvKpDMbSCI90Hlsl4cfE0=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ID+YQTHdxOAQwL3HkDNmN+gvKpDMbSCI90Hlsl4cfE0="}]}, {"Route": "_content1/MudBlazor/MudBlazor.min.js", "AssetFile": "_content1/MudBlazor/MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41476"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y4xauMcIt84Bvrj3olTDT2vGwa9ZzAIuG7+BSyVhhn8=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y4xauMcIt84Bvrj3olTDT2vGwa9ZzAIuG7+BSyVhhn8="}]}, {"Route": "_content1/MudBlazor/MudBlazor.min.r28yrlkyxf.js", "AssetFile": "_content1/MudBlazor/MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "41476"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y4xauMcIt84Bvrj3olTDT2vGwa9ZzAIuG7+BSyVhhn8=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-Y4xauMcIt84Bvrj3olTDT2vGwa9ZzAIuG7+BSyVhhn8="}, {"Name": "label", "Value": "_content1/MudBlazor/MudBlazor.min.js"}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "155758"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YLGeXaapI0/5IgZopewRJcFXomhRMlYYjugPLSyNjTY=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YLGeXaapI0/5IgZopewRJcFXomhRMlYYjugPLSyNjTY="}]}, {"Route": "css/bootstrap/bootstrap.min.css.kao5znno1s.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "625953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kao5znno1s"}, {"Name": "integrity", "Value": "sha256-xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "css/bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "625953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0="}]}, {"Route": "css/bootstrap/bootstrap.min.o2wlpouf80.css", "AssetFile": "css/bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "155758"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YLGeXaapI0/5IgZopewRJcFXomhRMlYYjugPLSyNjTY=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o2wlpouf80"}, {"Name": "integrity", "Value": "sha256-YLGeXaapI0/5IgZopewRJcFXomhRMlYYjugPLSyNjTY="}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}]}, {"Route": "css/open-iconic/FONT-LICENSE", "AssetFile": "css/open-iconic/FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4017"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"+Q44zfEaCMmXduni5Td+IgCbk8sSUQwES2nWs+KKQz0=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+Q44zfEaCMmXduni5Td+IgCbk8sSUQwES2nWs+KKQz0="}]}, {"Route": "css/open-iconic/FONT-LICENSE.z189sdz1gv", "AssetFile": "css/open-iconic/FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4017"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"+Q44zfEaCMmXduni5Td+IgCbk8sSUQwES2nWs+KKQz0=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z189sdz1gv"}, {"Name": "integrity", "Value": "sha256-+Q44zfEaCMmXduni5Td+IgCbk8sSUQwES2nWs+KKQz0="}, {"Name": "label", "Value": "css/open-iconic/FONT-LICENSE"}]}, {"Route": "css/open-iconic/ICON-LICENSE", "AssetFile": "css/open-iconic/ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1073"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"s/Is6Ey6jfNAEfXUIOyHrXXX+RcA8hzchYnuOIWUMl4=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s/Is6Ey6jfNAEfXUIOyHrXXX+RcA8hzchYnuOIWUMl4="}]}, {"Route": "css/open-iconic/ICON-LICENSE.3cg08az021", "AssetFile": "css/open-iconic/ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1073"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"s/Is6Ey6jfNAEfXUIOyHrXXX+RcA8hzchYnuOIWUMl4=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3cg08az021"}, {"Name": "integrity", "Value": "sha256-s/Is6Ey6jfNAEfXUIOyHrXXX+RcA8hzchYnuOIWUMl4="}, {"Name": "label", "Value": "css/open-iconic/ICON-LICENSE"}]}, {"Route": "css/open-iconic/README.l5877nzu3a.md", "AssetFile": "css/open-iconic/README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3501"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"9wdNXQFE78LCNHo+Hq2eXMTx+YBf2gjsufVTJc8dAV0=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l5877nzu3a"}, {"Name": "integrity", "Value": "sha256-9wdNXQFE78LCNHo+Hq2eXMTx+YBf2gjsufVTJc8dAV0="}, {"Name": "label", "Value": "css/open-iconic/README.md"}]}, {"Route": "css/open-iconic/README.md", "AssetFile": "css/open-iconic/README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3501"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"9wdNXQFE78LCNHo+Hq2eXMTx+YBf2gjsufVTJc8dAV0=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9wdNXQFE78LCNHo+Hq2eXMTx+YBf2gjsufVTJc8dAV0="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "AssetFile": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.0uw8dim9nl.eot", "AssetFile": "css/open-iconic/font/fonts/open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0uw8dim9nl"}, {"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.eot"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.10uzszoped.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "54789"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"oUpLdS+SoLJFwf4bzA3iKD7TCm66oLkTpAQlVJ2s1wc=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-oUpLdS+SoLJFwf4bzA3iKD7TCm66oLkTpAQlVJ2s1wc="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.eot", "AssetFile": "css/open-iconic/font/fonts/open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.h4d0pazwgy.woff", "AssetFile": "css/open-iconic/font/fonts/open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h4d0pazwgy"}, {"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.woff"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ll5grcv8wv.ttf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ll5grcv8wv"}, {"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.ttf"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg", "AssetFile": "css/open-iconic/font/fonts/open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "54789"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"oUpLdS+SoLJFwf4bzA3iKD7TCm66oLkTpAQlVJ2s1wc=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oUpLdS+SoLJFwf4bzA3iKD7TCm66oLkTpAQlVJ2s1wc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ttf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf", "AssetFile": "css/open-iconic/font/fonts/open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf"}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.woff", "AssetFile": "css/open-iconic/font/fonts/open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}]}, {"Route": "css/site.1vtxvcb14m.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "858"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"k8ttQt8b3auf2TSFFS+FpMy1PeccST9J5ozTv8hxRMs=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1vtxvcb14m"}, {"Name": "integrity", "Value": "sha256-k8ttQt8b3auf2TSFFS+FpMy1PeccST9J5ozTv8hxRMs="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "858"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"k8ttQt8b3auf2TSFFS+FpMy1PeccST9J5ozTv8hxRMs=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k8ttQt8b3auf2TSFFS+FpMy1PeccST9J5ozTv8hxRMs="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "js/mudblazor-fallback.h16zzeouc9.js", "AssetFile": "js/mudblazor-fallback.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4128"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VQT0AzzkmGUuFJ57C0wTKiXVqSc/2EIhztL6s05xpEM=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:56:05 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h16zzeouc9"}, {"Name": "integrity", "Value": "sha256-VQT0AzzkmGUuFJ57C0wTKiXVqSc/2EIhztL6s05xpEM="}, {"Name": "label", "Value": "js/mudblazor-fallback.js"}]}, {"Route": "js/mudblazor-fallback.js", "AssetFile": "js/mudblazor-fallback.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4128"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VQT0AzzkmGUuFJ57C0wTKiXVqSc/2EIhztL6s05xpEM=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:56:05 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VQT0AzzkmGUuFJ57C0wTKiXVqSc/2EIhztL6s05xpEM="}]}, {"Route": "js/script.js", "AssetFile": "js/script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "311"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4vzflePRBPWB54ir4xD8xg4t70z22erx1Y9VbA9qbPo=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4vzflePRBPWB54ir4xD8xg4t70z22erx1Y9VbA9qbPo="}]}, {"Route": "js/script.qlpid94yti.js", "AssetFile": "js/script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "311"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4vzflePRBPWB54ir4xD8xg4t70z22erx1Y9VbA9qbPo=\""}, {"Name": "Last-Modified", "Value": "Sat, 01 Feb 2025 22:03:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qlpid94yti"}, {"Name": "integrity", "Value": "sha256-4vzflePRBPWB54ir4xD8xg4t70z22erx1Y9VbA9qbPo="}, {"Name": "label", "Value": "js/script.js"}]}]}