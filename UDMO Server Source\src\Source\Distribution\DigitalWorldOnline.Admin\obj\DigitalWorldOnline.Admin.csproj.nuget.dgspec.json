{"format": 1, "restore": {"E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Distribution\\DigitalWorldOnline.Admin\\DigitalWorldOnline.Admin.csproj": {}}, "projects": {"E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Application\\DigitalWorldOnline.Application\\DigitalWorldOnline.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Application\\DigitalWorldOnline.Application\\DigitalWorldOnline.Application.csproj", "projectName": "DigitalWorldOnline.Application", "projectPath": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Application\\DigitalWorldOnline.Application\\DigitalWorldOnline.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Application\\DigitalWorldOnline.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Domain\\DigitalWorldOnline.Commons\\DigitalWorldOnline.Commons.csproj": {"projectPath": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Domain\\DigitalWorldOnline.Commons\\DigitalWorldOnline.Commons.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.9.0, )"}, "MediatR": {"target": "Package", "version": "[11.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Distribution\\DigitalWorldOnline.Admin\\DigitalWorldOnline.Admin.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Distribution\\DigitalWorldOnline.Admin\\DigitalWorldOnline.Admin.csproj", "projectName": "DigitalWorldOnline.Admin", "projectPath": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Distribution\\DigitalWorldOnline.Admin\\DigitalWorldOnline.Admin.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Distribution\\DigitalWorldOnline.Admin\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Infra\\DigitalWorldOnline.Infrastructure\\DigitalWorldOnline.Infrastructure.csproj": {"projectPath": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Infra\\DigitalWorldOnline.Infrastructure\\DigitalWorldOnline.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.0, )"}, "Blazored.LocalStorage": {"target": "Package", "version": "[4.3.0, )"}, "MediatR.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[11.0.0, )"}, "Microsoft.AspNetCore.Authentication.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.11, )"}, "Microsoft.Extensions.ApiDescription.Client": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.2, )"}, "Microsoft.Extensions.Configuration.UserSecrets": {"target": "Package", "version": "[7.0.0, )"}, "MudBlazor": {"target": "Package", "version": "[6.1.2, )"}, "NSwag.ApiDescription.Client": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[13.18.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Serilog.Formatting.Compact": {"target": "Package", "version": "[1.1.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[3.4.0, )"}, "Serilog.Sinks.RollingFile": {"target": "Package", "version": "[3.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Domain\\DigitalWorldOnline.Commons\\DigitalWorldOnline.Commons.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Domain\\DigitalWorldOnline.Commons\\DigitalWorldOnline.Commons.csproj", "projectName": "DigitalWorldOnline.Commons", "projectPath": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Domain\\DigitalWorldOnline.Commons\\DigitalWorldOnline.Commons.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Domain\\DigitalWorldOnline.Commons\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.11, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "System.Runtime": {"target": "Package", "version": "[4.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Infra\\DigitalWorldOnline.Infrastructure\\DigitalWorldOnline.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Infra\\DigitalWorldOnline.Infrastructure\\DigitalWorldOnline.Infrastructure.csproj", "projectName": "DigitalWorldOnline.Infrastructure", "projectPath": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Infra\\DigitalWorldOnline.Infrastructure\\DigitalWorldOnline.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Infra\\DigitalWorldOnline.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Application\\DigitalWorldOnline.Application\\DigitalWorldOnline.Application.csproj": {"projectPath": "E:\\Codes\\OpenDigimonMastersServer\\UDMO Server Source\\src\\Source\\Application\\DigitalWorldOnline.Application\\DigitalWorldOnline.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "MediatR": {"target": "Package", "version": "[11.1.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.11, )"}, "Quartz": {"target": "Package", "version": "[3.13.1, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.4.7, )"}, "System.Runtime": {"target": "Package", "version": "[4.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}