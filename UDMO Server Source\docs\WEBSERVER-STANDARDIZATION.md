# 🌐 WebServer/Admin Standardization

## 📋 Overview

The WebServer/Admin has been standardized to use the same configuration approach as other servers in the OpenDigimonMastersServer project.

## ✅ Changes Made

### 1. **Database Configuration Standardization**

**Before:**
```csharp
// Startup.cs - Line 90 (OLD)
services.AddDbContext<DatabaseContext>(options => 
    options.UseSqlServer(Configuration["Database:Connection"]));
```

**After:**
```csharp
// Startup.cs - Line 90 (NEW)
services.AddDbContext<DatabaseContext>();
```

**Benefits:**
- ✅ Now uses the same `DatabaseContext.OnConfiguring()` method as other servers
- ✅ Automatically supports `DMOX_CONNECTION_STRING` environment variable
- ✅ Follows the same priority order: Environment Variable > Configuration > Default
- ✅ Consistent with other servers (GameServer, RoutineServer, CharacterServer)

### 2. **PowerShell Script Creation**

**New File:** `StartWebServer.ps1`

**Features:**
- 🔄 Loads environment variables from `.env` file
- 🔍 Validates database connectivity
- 📍 Uses relative paths (no hardcoded absolute paths)
- 🌐 Displays web interface URLs
- ⚙️ Supports `-NoWait` parameter for background execution
- 📖 Includes help documentation with `-Help` parameter

**Usage:**
```powershell
# Start WebServer with console output
.\StartWebServer.ps1

# Start WebServer in background
.\StartWebServer.ps1 -NoWait

# Show help
.\StartWebServer.ps1 -Help
```

### 3. **ServerManager.ps1 Integration**

**Added WebServer to the main server manager:**
- 🌐 WebServer/Admin now appears in the server list
- 🎮 Can be started/stopped alongside other servers
- 📊 Shows status in the server status display
- 🔧 Integrated with "Start All Servers" functionality

## 🚀 How to Use

### Option 1: Individual WebServer Script
```powershell
# Navigate to project root
cd "UDMO Server Source"

# Start WebServer
.\StartWebServer.ps1
```

### Option 2: ServerManager Integration
```powershell
# Navigate to project root
cd "UDMO Server Source"

# Start server manager
.\ServerManager.ps1

# Choose option 2 (Start Individual Server)
# Select WebServer/Admin from the list
```

### Option 3: Start All Servers
```powershell
# Navigate to project root
cd "UDMO Server Source"

# Start server manager
.\ServerManager.ps1

# Choose option 1 (Start All Servers)
# WebServer will be included automatically
```

## 🌐 Web Interface Access

Once started, the WebServer/Admin interface will be available at:
- **Primary URL:** http://localhost:41001
- **Secondary URL:** http://localhost:5002

## 📁 File Structure

```
UDMO Server Source/
├── StartWebServer.ps1                    # ✅ NEW - Individual WebServer launcher
├── ServerManager.ps1                     # ✅ UPDATED - Now includes WebServer
├── src/Source/Distribution/
│   └── DigitalWorldOnline.Admin/
│       └── Startup.cs                    # ✅ UPDATED - Standardized DB config
└── docs/
    └── WEBSERVER-STANDARDIZATION.md     # ✅ NEW - This documentation
```

## 🔧 Technical Details

### Environment Variable Support

The WebServer now supports the same environment variables as other servers:

```bash
# .env file
DMOX_CONNECTION_STRING=Server=localhost\SQLEXPRESS;Database=DMOX;Integrated Security=true;TrustServerCertificate=True
```

### Connection String Priority Order

1. **Environment Variable:** `DMOX_CONNECTION_STRING`
2. **Configuration:** `appsettings.json` → `ConnectionStrings:Digimon`
3. **Default:** Built-in development connection string

### Cross-Platform Compatibility

- ✅ Uses relative paths instead of absolute paths
- ✅ Works on Windows, macOS, and Linux
- ✅ PowerShell Core compatible
- ✅ No hardcoded drive letters or user-specific paths

## 🔧 wwwroot Path Resolution

### Problem Solved: Automatic Content Root Detection

The WebServer now automatically finds the correct `wwwroot` folder regardless of the working directory:

**Before:**
- WebServer looked for `wwwroot` in the current working directory
- Failed when started from different locations (e.g., from `bin/Release/net8.0/`)

**After:**
- Intelligent content root detection in `Program.cs`
- Searches upward from executable location to find project directory
- Fallback paths for common build output locations
- Works consistently across different environments

**Technical Implementation:**
```csharp
// Automatic content root detection
var contentRoot = GetContentRoot();
if (!string.IsNullOrEmpty(contentRoot))
{
    webBuilder.UseContentRoot(contentRoot);
}
```

**Search Strategy:**
1. **Primary:** Search upward from executable directory for `wwwroot` folder
2. **Secondary:** Look for `.csproj` file + `wwwroot` in same directory
3. **Fallback:** Try relative paths from common build locations
4. **Default:** Use ASP.NET Core default behavior if all else fails

## 🐛 Troubleshooting

### WebServer Won't Start

1. **Check if executable exists:**
   ```powershell
   Test-Path "src\Source\Distribution\DigitalWorldOnline.Admin\bin\Release\net8.0\DigitalWorldOnline.Admin.exe"
   ```

2. **Build the solution:**
   ```powershell
   dotnet build DigitalWorldOnline.sln --configuration Release
   ```

3. **Verify .env file:**
   ```powershell
   Test-Path ".env"
   ```

### Database Connection Issues

1. **Check environment variable:**
   ```powershell
   [Environment]::GetEnvironmentVariable("DMOX_CONNECTION_STRING")
   ```

2. **Verify SQL Server is running**

3. **Test connection string format**

### Port Already in Use

If ports 41001 or 5002 are already in use:
1. Stop other applications using these ports
2. Or modify the ports in `Program.cs` if needed

## 📝 Migration Notes

### For Developers

- **No breaking changes** to existing functionality
- **Backward compatible** with existing configurations
- **Enhanced** with environment variable support

### For Server Administrators

- **Update scripts** to use new PowerShell launchers
- **Configure .env file** for consistent environment management
- **Use ServerManager.ps1** for centralized server management

## 🎯 Benefits

1. **Consistency:** All servers now use the same configuration approach
2. **Flexibility:** Environment variables allow easy configuration changes
3. **Portability:** Relative paths work across different environments
4. **Management:** Centralized server management through ServerManager.ps1
5. **Documentation:** Clear usage instructions and troubleshooting guides
