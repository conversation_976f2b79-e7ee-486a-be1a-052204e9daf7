# OpenDigimonMastersServer - WebServer/Admin Manager
# PowerShell script with clean interface and proper environment variable handling

param(
    [string]$Action = "start"
)

# Set console encoding to UTF-8 for proper emoji display
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "OpenDigimonMastersServer - WebServer/Admin"

# Global variable for server process
$global:WebServerProcess = $null

function Write-Header {
    Clear-Host
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "   OpenDigimonMastersServer - WebServer" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
}

function Load-EnvironmentVariables {
    Write-Host "Loading environment variables from .env file..." -ForegroundColor Yellow
    
    if (-not (Test-Path ".env")) {
        Write-Host "ERROR: .env file not found!" -ForegroundColor Red
        Write-Host "Please copy .env.example to .env and configure your settings" -ForegroundColor Yellow
        return $false
    }
    
    $envContent = Get-Content ".env" -Encoding UTF8
    $variablesLoaded = 0
    
    foreach ($line in $envContent) {
        if ($line -and !$line.StartsWith("#") -and $line.Contains("=")) {
            $parts = $line.Split("=", 2)
            if ($parts.Length -eq 2) {
                $name = $parts[0].Trim()
                $value = $parts[1].Trim()
                
                # Remove quotes if present
                if ($value.StartsWith('"') -and $value.EndsWith('"')) {
                    $value = $value.Substring(1, $value.Length - 2)
                }
                
                [Environment]::SetEnvironmentVariable($name, $value, "Process")
                Write-Host "  Loaded: $name" -ForegroundColor Gray
                $variablesLoaded++
            }
        }
    }
    
    Write-Host "Environment variables loaded successfully! ($variablesLoaded variables)" -ForegroundColor Green
    
    # Verify critical variables
    $dbConnection = [Environment]::GetEnvironmentVariable("DMOX_CONNECTION_STRING")
    if ($dbConnection) {
        $maskedConnection = $dbConnection -replace "Password=[^;]*", "Password=***"
        Write-Host "Database: $($maskedConnection.Substring(0, [Math]::Min(50, $maskedConnection.Length)))..." -ForegroundColor Cyan
    } else {
        Write-Host "WARNING: DMOX_CONNECTION_STRING not found in .env file!" -ForegroundColor Yellow
        Write-Host "WebServer will use default connection string or appsettings.json" -ForegroundColor Yellow
    }
    
    Write-Host ""
    return $true
}

function Get-WebServerConfig {
    return @{
        Path = "src\Source\Distribution\DigitalWorldOnline.Admin\bin\Release\net8.0\DigitalWorldOnline.Admin.exe"
        WorkingDirectory = "src\Source\Distribution\DigitalWorldOnline.Admin"
        Name = "WebServer/Admin Panel"
        Icon = "WebServer"
        Port = "41001"
        SecondaryPort = "5002"
        Url = "http://localhost:41001"
    }
}

function Test-ServerExecutable {
    param([hashtable]$Config)
    
    if (-not (Test-Path $Config.Path)) {
        Write-Host "ERROR: WebServer executable not found!" -ForegroundColor Red
        Write-Host "Expected location: $($Config.Path)" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Please build the solution first:" -ForegroundColor Yellow
        Write-Host "  dotnet build DigitalWorldOnline.sln --configuration Release" -ForegroundColor White
        return $false
    }
    
    return $true
}

function Fix-StaticWebAssets {
    param([hashtable]$Config)

    $staticAssetsFile = Join-Path (Split-Path $Config.Path -Parent) "DigitalWorldOnline.Admin.staticwebassets.runtime.json"

    if (Test-Path $staticAssetsFile) {
        Write-Host "  Removing problematic static web assets file..." -ForegroundColor Gray

        # Remove the problematic file to force ASP.NET Core to use local wwwroot only
        Remove-Item $staticAssetsFile -Force

        Write-Host "  Static web assets file removed - using local wwwroot only." -ForegroundColor Gray
    }
}

function Start-WebServer {
    param([hashtable]$Config)

    Write-Host "Starting $($Config.Name)..." -ForegroundColor Green

    if (-not (Test-ServerExecutable -Config $Config)) {
        return $false
    }

    # Fix static web assets paths before starting
    Fix-StaticWebAssets -Config $Config

    try {
        # Use the project directory as working directory to resolve static assets correctly
        $serverDir = $Config.WorkingDirectory
        $executablePath = Resolve-Path $Config.Path
        
        # Start the server process
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = $executablePath
        $processInfo.WorkingDirectory = $serverDir
        $processInfo.UseShellExecute = $false
        $processInfo.CreateNoWindow = $false
        $processInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Normal

        # Copy environment variables to the process
        foreach ($envVar in [Environment]::GetEnvironmentVariables("Process").GetEnumerator()) {
            $processInfo.EnvironmentVariables[$envVar.Key] = $envVar.Value
        }
        
        $global:WebServerProcess = [System.Diagnostics.Process]::Start($processInfo)
        
        Write-Host "  Started successfully (PID: $($global:WebServerProcess.Id))" -ForegroundColor Green
        Write-Host "  Admin Panel URL: $($Config.Url)" -ForegroundColor Cyan
        Write-Host "  Primary Port: $($Config.Port)" -ForegroundColor Gray
        Write-Host "  Secondary Port: $($Config.SecondaryPort)" -ForegroundColor Gray
        Write-Host ""
        
        return $true
    }
    catch {
        Write-Host "ERROR: Failed to start $($Config.Name): $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Stop-WebServer {
    if ($global:WebServerProcess -and -not $global:WebServerProcess.HasExited) {
        Write-Host "Stopping WebServer..." -ForegroundColor Yellow
        try {
            $global:WebServerProcess.Kill()
            $global:WebServerProcess.WaitForExit(5000)
            Write-Host "WebServer stopped successfully." -ForegroundColor Green
        }
        catch {
            Write-Host "Warning: Error stopping WebServer: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "WebServer is not running." -ForegroundColor Gray
    }
    
    $global:WebServerProcess = $null
}

function Start-WebServerDirect {
    Write-Header
    
    if (-not (Load-EnvironmentVariables)) {
        Read-Host "Press Enter to exit"
        return
    }
    
    $config = Get-WebServerConfig
    
    if (Start-WebServer -Config $config) {
        Write-Host "WebServer started successfully!" -ForegroundColor Green
        Write-Host "Press Ctrl+C to stop the server or close this window." -ForegroundColor Yellow
        
        # Keep the script running and monitor the process
        try {
            while ($global:WebServerProcess -and -not $global:WebServerProcess.HasExited) {
                Start-Sleep -Seconds 5
            }
        }
        catch {
            Write-Host "Stopping WebServer..." -ForegroundColor Yellow
        }
        finally {
            Stop-WebServer
        }
    } else {
        Read-Host "Press Enter to exit"
    }
}

# Handle Ctrl+C gracefully
$null = Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
    Stop-WebServer
}

# Main execution
switch ($Action.ToLower()) {
    "start" { Start-WebServerDirect }
    "stop" { 
        Write-Header
        Stop-WebServer
    }
    default { 
        Write-Host "OpenDigimonMastersServer - WebServer Manager" -ForegroundColor Cyan
        Write-Host "Usage:" -ForegroundColor White
        Write-Host "  .\StartWebServer-New.ps1          # Start WebServer directly" -ForegroundColor Gray
        Write-Host "  .\StartWebServer-New.ps1 -Action stop   # Stop WebServer" -ForegroundColor Gray
    }
}
