using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using System.IO;
using System.Reflection;

namespace DigitalWorldOnline.Admin
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args).ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                    webBuilder.UseUrls("http://localhost:41001", "http://localhost:5002");

                    // Set content root to project directory to find wwwroot
                    // This ensures wwwroot is found regardless of working directory
                    var contentRoot = GetContentRoot();
                    if (!string.IsNullOrEmpty(contentRoot))
                    {
                        webBuilder.UseContentRoot(contentRoot);
                    }
                });

        /// <summary>
        /// Gets the content root directory for the web application.
        /// Searches for the project directory containing wwwroot folder.
        /// </summary>
        private static string GetContentRoot()
        {
            try
            {
                // Get the directory where the executable is located
                var assemblyLocation = Assembly.GetExecutingAssembly().Location;
                var executableDir = Path.GetDirectoryName(assemblyLocation);

                if (string.IsNullOrEmpty(executableDir))
                    return null;

                // Search upwards from executable directory to find project root
                var currentDir = new DirectoryInfo(executableDir);

                while (currentDir != null)
                {
                    // Look for wwwroot folder in current directory
                    var wwwrootPath = Path.Combine(currentDir.FullName, "wwwroot");
                    if (Directory.Exists(wwwrootPath))
                    {
                        return currentDir.FullName;
                    }

                    // Look for project file (.csproj) and wwwroot in same directory
                    var projectFiles = currentDir.GetFiles("*.csproj");
                    if (projectFiles.Length > 0 && Directory.Exists(wwwrootPath))
                    {
                        return currentDir.FullName;
                    }

                    // Move up one directory level
                    currentDir = currentDir.Parent;

                    // Safety check: don't go beyond reasonable depth
                    if (currentDir?.FullName.Length < 10)
                        break;
                }

                // Fallback: try relative paths from common locations
                var fallbackPaths = new[]
                {
                    "../../../../..", // From bin/Release/net8.0 to project root
                    "../../../..",    // From bin/Debug/net8.0 to project root
                    "../../..",       // From bin/Release to project root
                    "../..",          // From bin to project root
                    "."               // Current directory
                };

                foreach (var relativePath in fallbackPaths)
                {
                    try
                    {
                        var testPath = Path.GetFullPath(Path.Combine(executableDir, relativePath));
                        var wwwrootTest = Path.Combine(testPath, "wwwroot");

                        if (Directory.Exists(wwwrootTest))
                        {
                            return testPath;
                        }
                    }
                    catch
                    {
                        // Ignore path resolution errors
                    }
                }
            }
            catch
            {
                // If anything fails, return null to use default behavior
            }

            return null;
        }
    }
}
