using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System.IO;
using System.Reflection;

namespace DigitalWorldOnline.Admin
{
    public class Program
    {
        public static void Main(string[] args)
        {
            // Get the executable directory to use as content root
            var contentRoot = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);

            // Remove any static web assets manifest files that might cause issues
            CleanStaticWebAssetsManifests(contentRoot);

            CreateHostBuilder(args, contentRoot).Build().Run();
        }

        private static void CleanStaticWebAssetsManifests(string contentRoot)
        {
            try
            {
                var manifestFiles = Directory.GetFiles(contentRoot, "*.staticwebassets.runtime.json", SearchOption.AllDirectories);
                foreach (var file in manifestFiles)
                {
                    try
                    {
                        File.Delete(file);
                    }
                    catch
                    {
                        // Ignore errors when deleting manifest files
                    }
                }
            }
            catch
            {
                // Ignore errors when searching for manifest files
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args, string contentRoot)
        {
            return Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration((context, config) =>
                {
                    // Clear any existing configuration sources that might load static web assets
                    config.Sources.Clear();

                    // Add only the configuration sources we need
                    config.SetBasePath(contentRoot);
                    config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
                    config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true, reloadOnChange: true);
                    config.AddEnvironmentVariables();
                    config.AddCommandLine(args);
                })
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                    webBuilder.UseUrls("http://localhost:41001", "http://localhost:5002");
                    webBuilder.UseContentRoot(contentRoot);
                    webBuilder.UseWebRoot(Path.Combine(contentRoot, "wwwroot"));

                    // Disable static web assets completely
                    webBuilder.UseSetting("ASPNETCORE_PREVENTHOSTINGSTARTUP", "true");
                });
        }
    }
}
