// MudBlazor Fallback JavaScript
// This script provides fallback functions for MudBlazor when the main JS file fails to load

(function() {
    'use strict';
    
    console.log('MudBlazor Fallback Script Loading...');
    
    // Wait for DOM to be ready
    function domReady(fn) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', fn);
        } else {
            fn();
        }
    }
    
    // Initialize fallback functions
    function initializeFallbacks() {
        console.log('Initializing MudBlazor fallbacks...');
        
        // Create mudElementRef object if it doesn't exist
        if (typeof window.mudElementRef === 'undefined') {
            window.mudElementRef = {
                getBoundingClientRect: function() {
                    console.log('Using fallback getBoundingClientRect');
                    return {
                        top: 0,
                        left: 0,
                        width: 0,
                        height: 0,
                        right: 0,
                        bottom: 0,
                        x: 0,
                        y: 0
                    };
                },
                focus: function() {
                    console.log('Using fallback focus');
                },
                blur: function() {
                    console.log('Using fallback blur');
                },
                scrollIntoView: function() {
                    console.log('Using fallback scrollIntoView');
                }
            };
        }
        
        // Create MudBlazor namespace if it doesn't exist
        if (typeof window.MudBlazor === 'undefined') {
            window.MudBlazor = {
                utilities: {
                    getBoundingClientRect: function(element) {
                        if (element && element.getBoundingClientRect) {
                            return element.getBoundingClientRect();
                        }
                        return {
                            top: 0,
                            left: 0,
                            width: 0,
                            height: 0,
                            right: 0,
                            bottom: 0,
                            x: 0,
                            y: 0
                        };
                    },
                    
                    scrollToElement: function(element) {
                        if (element && element.scrollIntoView) {
                            element.scrollIntoView({ behavior: 'smooth' });
                        }
                    },
                    
                    focusElement: function(element) {
                        if (element && element.focus) {
                            element.focus();
                        }
                    }
                }
            };
        }
        
        // Add global functions that MudBlazor might need
        window.mudGetBoundingClientRect = function(element) {
            if (element && element.getBoundingClientRect) {
                return element.getBoundingClientRect();
            }
            return {
                top: 0,
                left: 0,
                width: 0,
                height: 0,
                right: 0,
                bottom: 0,
                x: 0,
                y: 0
            };
        };
        
        console.log('MudBlazor fallbacks initialized successfully');
    }
    
    // Check if MudBlazor is loaded properly
    function checkMudBlazorLoaded() {
        setTimeout(function() {
            if (typeof window.mudElementRef === 'undefined' || 
                typeof window.MudBlazor === 'undefined') {
                console.warn('MudBlazor not fully loaded, using fallbacks');
                initializeFallbacks();
            } else {
                console.log('MudBlazor loaded successfully');
            }
        }, 1000);
    }
    
    // Initialize when DOM is ready
    domReady(function() {
        initializeFallbacks();
        checkMudBlazorLoaded();
    });
    
})();
