{
	"DetailedErrors": true,
	"Logging": {
		"LogLevel": {
			"Default": "Debug",
			"Microsoft": "Debug",
			"Microsoft.Hosting.Lifetime": "Debug"
		}
	},
	"AuthenticationServer": {
		"Address": "0.0.0.0",
		"Port": "7029",
		"Backlog": "5",
		"UseHash": true,
		"AllowRegisterOnLogin": false
	},
	"GatewayServer": {
		"Address": "0.0.0.0",
		"Port": "8074"
	},
	"CharacterServer": {
		"Address": "0.0.0.0" // localhost
	},
	"Log": {
		"DebugRepository": "logs/Account/Debug/log",
		"WarningRepository": "logs/Account/Warning/log",
		"InformationRepository": "logs/Account/Information/log",
		"ErrorRepository": "logs/Account/Error/log",
		"VerboseRepository": "logs/Account/Verbose/log"
	}
}
