@echo off
:: Verifica se o script está sendo executado como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Solicitar permissões de administrador...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo.
echo ========================================
echo   OpenDigimonMastersServer - WebServer
echo ========================================
echo.

:: Carregar variáveis de ambiente do arquivo .env
echo Loading environment variables from .env file...
if exist ".env" (
    for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
        if not "%%a"=="" if not "%%a"=="REM" if not "%%a"=="#" (
            set "%%a=%%b"
            echo   Loaded: %%a
        )
    )
    echo Environment variables loaded successfully.
) else (
    echo Warning: .env file not found. Using default configuration.
)

echo.

:: De<PERSON>ir as variáveis de caminho
set caminho2=%~dp0\src\Source\Distribution\DigitalWorldOnline.Admin\bin\Release\net8.0\DigitalWorldOnline.Admin.exe

:: Espera 1 segundo
timeout /t 1 /nobreak > nul

:: Executa o programa
echo Starting WebServer/Admin...
start "" "%caminho2%"
